import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { AuthService } from '../../../../core/services/auth.service';
import { StudioRequestsService } from '../../../../core/services/studio-requests.service';
import { User } from '../../../../core/models/auth.models';
import { StudioRequest, STUDIO_REQUEST_STATUS_LABELS, STUDIO_REQUEST_STATUS_COLORS } from '../../../../core/models/studio-request.models';

@Component({
  selector: 'app-dashboard',
  standalone: false,
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent implements OnInit, OnDestroy {
  currentUser: User | null = null;
  studioRequests: StudioRequest[] = [];
  isLoadingRequests = false;
  requestsError = '';
  private destroy$ = new Subject<void>();

  // Expose constants to template
  readonly statusLabels = STUDIO_REQUEST_STATUS_LABELS;
  readonly statusColors = STUDIO_REQUEST_STATUS_COLORS;

  constructor(
    private authService: AuthService,
    private studioRequestsService: StudioRequestsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(authState => {
        if (!authState.isAuthenticated) {
          this.router.navigate(['/login']);
          return;
        }
        this.currentUser = authState.user;
        this.loadStudioRequests();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onLogout(): void {
    this.authService.logout();
  }

  navigateToStudio(): void {
    this.router.navigate(['/studio']);
  }

  navigateToMain(): void {
    this.router.navigate(['/']);
  }

  loadStudioRequests(): void {
    this.isLoadingRequests = true;
    this.requestsError = '';

    this.studioRequestsService.getAllRequests()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (requests) => {
          this.studioRequests = requests;
          this.isLoadingRequests = false;
        },
        error: (error) => {
          this.isLoadingRequests = false;
          this.handleRequestsError(error);
        }
      });
  }

  private handleRequestsError(error: any): void {
    if (error.status === 401) {
      this.requestsError = 'Ошибка авторизации. Попробуйте войти заново.';
      this.authService.logout();
    } else if (error.status === 403) {
      this.requestsError = 'Недостаточно прав для просмотра заявок.';
    } else if (error.status === 0) {
      this.requestsError = 'Ошибка подключения к серверу.';
    } else {
      this.requestsError = 'Ошибка при загрузке заявок.';
    }
  }

  refreshRequests(): void {
    this.loadStudioRequests();
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  updateRequestStatus(request: StudioRequest, newStatus: 'new' | 'in_progress' | 'done'): void {
    this.studioRequestsService.updateRequest(request.id, { status: newStatus })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedRequest) => {
          const index = this.studioRequests.findIndex(r => r.id === request.id);
          if (index !== -1) {
            this.studioRequests[index] = updatedRequest;
          }
        },
        error: (error) => {
          console.error('Error updating request status:', error);
        }
      });
  }
}
